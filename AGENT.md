# AGENT.md - Codebase Guide for AI Agents

## Core Project: MarkItDown Document Converter

This is a Digital Garden repository containing MarkItDown, a Python utility for converting documents to Markdown.

### Build/Test/Lint Commands

```bash
cd Package/MarkItDown/packages/markitdown  # Navigate to main package
pip install hatch                           # Install build tool
hatch shell                                 # Activate environment
hatch test                                  # Run all tests
hatch test tests/test_module_misc.py::test_version  # Run single test
pre-commit run --all-files                  # Run linting/formatting
python -m markitdown file.pdf               # Run CLI tool
```

### Architecture & Structure

- **Main package**: `Package/MarkItDown/packages/markitdown/` - Core document conversion library
- **MCP server**: `Package/MarkItDown/packages/markitdown-mcp/` - Model Context Protocol server
- **Sample plugin**: `Package/MarkItDown/packages/markitdown-sample-plugin/` - Plugin development example
- **Digital Garden**: Root directory contains Obsidian-based knowledge management system
- **Requirements**: Python 3.10+, uses <PERSON> for build system and dependency management

### Code Style & Conventions

- **Imports**: Standard lib → third-party → local (with blank lines between groups)
- **Typing**: Comprehensive annotations with `Union`, `Optional`, `List[Any]`, `BinaryIO`
- **Naming**: snake_case for functions/variables, PascalCase for classes, _private for internal
- **Error handling**: Custom exception hierarchy, graceful degradation, dependency checking patterns
- **Formatting**: Black formatter, pre-commit hooks for code quality
- **Testing**: pytest framework with parametrized tests and coverage reporting
