{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-01T02:44:39.983Z", "updatedAt": "2025-08-01T02:44:39.996Z", "resourceCount": 25}, "resources": [{"id": "black-widow", "source": "project", "protocol": "role", "name": "Black Widow 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/black-widow/black-widow.role.md", "metadata": {"createdAt": "2025-08-01T02:44:39.984Z", "updatedAt": "2025-08-01T02:44:39.984Z", "scannedAt": "2025-08-01T02:44:39.984Z", "path": "role/black-widow/black-widow.role.md"}}, {"id": "intelligence-workflow", "source": "project", "protocol": "execution", "name": "Intelligence Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/intelligence-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.985Z", "updatedAt": "2025-08-01T02:44:39.985Z", "scannedAt": "2025-08-01T02:44:39.985Z", "path": "role/black-widow/execution/intelligence-workflow.execution.md"}}, {"id": "research-methodology", "source": "project", "protocol": "execution", "name": "Research Methodology 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/research-methodology.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.985Z", "updatedAt": "2025-08-01T02:44:39.985Z", "scannedAt": "2025-08-01T02:44:39.985Z", "path": "role/black-widow/execution/research-methodology.execution.md"}}, {"id": "risk-analysis", "source": "project", "protocol": "execution", "name": "Risk Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/risk-analysis.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.985Z", "updatedAt": "2025-08-01T02:44:39.985Z", "scannedAt": "2025-08-01T02:44:39.985Z", "path": "role/black-widow/execution/risk-analysis.execution.md"}}, {"id": "intelligence-analysis", "source": "project", "protocol": "thought", "name": "Intelligence Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/intelligence-analysis.thought.md", "metadata": {"createdAt": "2025-08-01T02:44:39.986Z", "updatedAt": "2025-08-01T02:44:39.986Z", "scannedAt": "2025-08-01T02:44:39.986Z", "path": "role/black-widow/thought/intelligence-analysis.thought.md"}}, {"id": "pattern-recognition", "source": "project", "protocol": "thought", "name": "Pattern Recognition 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/pattern-recognition.thought.md", "metadata": {"createdAt": "2025-08-01T02:44:39.989Z", "updatedAt": "2025-08-01T02:44:39.989Z", "scannedAt": "2025-08-01T02:44:39.989Z", "path": "role/black-widow/thought/pattern-recognition.thought.md"}}, {"id": "risk-assessment", "source": "project", "protocol": "thought", "name": "Risk Assessment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/risk-assessment.thought.md", "metadata": {"createdAt": "2025-08-01T02:44:39.990Z", "updatedAt": "2025-08-01T02:44:39.990Z", "scannedAt": "2025-08-01T02:44:39.990Z", "path": "role/black-widow/thought/risk-assessment.thought.md"}}, {"id": "dialogue-management", "source": "project", "protocol": "execution", "name": "Dialogue Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/dialogue-management.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.991Z", "updatedAt": "2025-08-01T02:44:39.991Z", "scannedAt": "2025-08-01T02:44:39.991Z", "path": "role/fury/execution/dialogue-management.execution.md"}}, {"id": "fury-workflow", "source": "project", "protocol": "execution", "name": "Fury Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/fury-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.991Z", "updatedAt": "2025-08-01T02:44:39.991Z", "scannedAt": "2025-08-01T02:44:39.991Z", "path": "role/fury/execution/fury-workflow.execution.md"}}, {"id": "resume-generation", "source": "project", "protocol": "execution", "name": "Resume Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/resume-generation.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.991Z", "updatedAt": "2025-08-01T02:44:39.991Z", "scannedAt": "2025-08-01T02:44:39.991Z", "path": "role/fury/execution/resume-generation.execution.md"}}, {"id": "fury", "source": "project", "protocol": "role", "name": "Fury 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fury/fury.role.md", "metadata": {"createdAt": "2025-08-01T02:44:39.991Z", "updatedAt": "2025-08-01T02:44:39.991Z", "scannedAt": "2025-08-01T02:44:39.991Z", "path": "role/fury/fury.role.md"}}, {"id": "agent-broker-mindset", "source": "project", "protocol": "thought", "name": "Agent Broker Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/agent-broker-mindset.thought.md", "metadata": {"createdAt": "2025-08-01T02:44:39.992Z", "updatedAt": "2025-08-01T02:44:39.992Z", "scannedAt": "2025-08-01T02:44:39.992Z", "path": "role/fury/thought/agent-broker-mindset.thought.md"}}, {"id": "value-discovery-techniques", "source": "project", "protocol": "thought", "name": "Value Discovery Techniques 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/value-discovery-techniques.thought.md", "metadata": {"createdAt": "2025-08-01T02:44:39.992Z", "updatedAt": "2025-08-01T02:44:39.992Z", "scannedAt": "2025-08-01T02:44:39.992Z", "path": "role/fury/thought/value-discovery-techniques.thought.md"}}, {"id": "core-management", "source": "project", "protocol": "execution", "name": "Core Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/core-management.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.993Z", "updatedAt": "2025-08-01T02:44:39.993Z", "scannedAt": "2025-08-01T02:44:39.993Z", "path": "role/pepper/execution/core-management.execution.md"}}, {"id": "tool-orchestration", "source": "project", "protocol": "execution", "name": "Tool Orchestration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/tool-orchestration.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.993Z", "updatedAt": "2025-08-01T02:44:39.993Z", "scannedAt": "2025-08-01T02:44:39.993Z", "path": "role/pepper/execution/tool-orchestration.execution.md"}}, {"id": "pepper", "source": "project", "protocol": "role", "name": "Pepper 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pepper/pepper.role.md", "metadata": {"createdAt": "2025-08-01T02:44:39.993Z", "updatedAt": "2025-08-01T02:44:39.993Z", "scannedAt": "2025-08-01T02:44:39.993Z", "path": "role/pepper/pepper.role.md"}}, {"id": "adaptive-learning", "source": "project", "protocol": "thought", "name": "Adaptive Learning 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/adaptive-learning.thought.md", "metadata": {"createdAt": "2025-08-01T02:44:39.994Z", "updatedAt": "2025-08-01T02:44:39.994Z", "scannedAt": "2025-08-01T02:44:39.994Z", "path": "role/pepper/thought/adaptive-learning.thought.md"}}, {"id": "verification-mindset", "source": "project", "protocol": "thought", "name": "Verification Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/verification-mindset.thought.md", "metadata": {"createdAt": "2025-08-01T02:44:39.994Z", "updatedAt": "2025-08-01T02:44:39.994Z", "scannedAt": "2025-08-01T02:44:39.994Z", "path": "role/pepper/thought/verification-mindset.thought.md"}}, {"id": "vision-document-management", "source": "project", "protocol": "execution", "name": "Vision Document Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-document-management.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.994Z", "updatedAt": "2025-08-01T02:44:39.994Z", "scannedAt": "2025-08-01T02:44:39.994Z", "path": "role/vision/execution/vision-document-management.execution.md"}}, {"id": "vision-enhanced-task-workflow", "source": "project", "protocol": "execution", "name": "Vision Enhanced Task Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-enhanced-task-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T02:44:39.995Z", "updatedAt": "2025-08-01T02:44:39.995Z", "scannedAt": "2025-08-01T02:44:39.995Z", "path": "role/vision/execution/vision-enhanced-task-workflow.execution.md"}}, {"id": "shrimp-task-manager-tools", "source": "project", "protocol": "knowledge", "name": "Shrimp Task Manager Tools 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/vision/knowledge/shrimp-task-manager-tools.knowledge.md", "metadata": {"createdAt": "2025-08-01T02:44:39.995Z", "updatedAt": "2025-08-01T02:44:39.995Z", "scannedAt": "2025-08-01T02:44:39.995Z", "path": "role/vision/knowledge/shrimp-task-manager-tools.knowledge.md"}}, {"id": "zhi-interaction-protocol", "source": "project", "protocol": "knowledge", "name": "Zhi Interaction Protocol 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/vision/knowledge/zhi-interaction-protocol.knowledge.md", "metadata": {"createdAt": "2025-08-01T02:44:39.995Z", "updatedAt": "2025-08-01T02:44:39.995Z", "scannedAt": "2025-08-01T02:44:39.995Z", "path": "role/vision/knowledge/zhi-interaction-protocol.knowledge.md"}}, {"id": "vision-analytical-mind", "source": "project", "protocol": "thought", "name": "Vision Analytical Mind 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-analytical-mind.thought.md", "metadata": {"createdAt": "2025-08-01T02:44:39.996Z", "updatedAt": "2025-08-01T02:44:39.996Z", "scannedAt": "2025-08-01T02:44:39.996Z", "path": "role/vision/thought/vision-analytical-mind.thought.md"}}, {"id": "vision-task-strategy", "source": "project", "protocol": "thought", "name": "Vision Task Strategy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-task-strategy.thought.md", "metadata": {"createdAt": "2025-08-01T02:44:39.996Z", "updatedAt": "2025-08-01T02:44:39.996Z", "scannedAt": "2025-08-01T02:44:39.996Z", "path": "role/vision/thought/vision-task-strategy.thought.md"}}, {"id": "vision", "source": "project", "protocol": "role", "name": "Vision 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/vision/vision.role.md", "metadata": {"createdAt": "2025-08-01T02:44:39.996Z", "updatedAt": "2025-08-01T02:44:39.996Z", "scannedAt": "2025-08-01T02:44:39.996Z", "path": "role/vision/vision.role.md"}}], "stats": {"totalResources": 25, "byProtocol": {"role": 4, "execution": 10, "thought": 9, "knowledge": 2}, "bySource": {"project": 25}}}